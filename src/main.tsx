import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'
import { performanceUtils } from './utils/performance'
import { registerSW } from './utils/serviceWorker'
import { preloadCriticalRoutes, setupRoutePreloading } from './utils/routePreloader'
import { startWebVitalsMonitoring } from './utils/webVitals'

// Initialize performance monitoring (production-safe)
performanceUtils.measureCoreWebVitals();
performanceUtils.trackBundleSize();

// Register service worker for caching
registerSW();

// Setup route preloading for better navigation performance
setupRoutePreloading();
preloadCriticalRoutes();

// Start Core Web Vitals monitoring for production
startWebVitalsMonitoring();

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
