import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react()
    // Temporarily disabled console removal plugin to fix build issues
    // TODO: Re-enable with better implementation if needed
  ],
  build: {
    // Enable minification and optimization (using esbuild which is faster)
    minify: 'esbuild',
    // Optimize for modern browsers
    target: 'es2020',
    // Enable code splitting for better lazy loading
    rollupOptions: {
      // Enhanced tree shaking
      treeshake: {
        moduleSideEffects: false,
        propertyReadSideEffects: false,
        tryCatchDeoptimization: false
      },
      output: {
        // Optimize chunk naming for better caching
        chunkFileNames: 'assets/[name]-[hash].js',
        entryFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]',
        manualChunks: (id) => {
          // Enhanced granular chunking strategy for better performance
          if (id.includes('node_modules')) {
            // Split React libraries more granularly
            if (id.includes('react-dom')) {
              return 'react-dom';
            }
            if (id.includes('react/') && !id.includes('react-dom') && !id.includes('react-router')) {
              return 'react-core';
            }

            // React Router - separate chunk
            if (id.includes('react-router')) {
              return 'react-router';
            }

            // Firebase - more granular splitting
            if (id.includes('firebase/app') || id.includes('@firebase/app')) {
              return 'firebase-core';
            }
            if (id.includes('firebase/auth') || id.includes('@firebase/auth')) {
              return 'firebase-auth';
            }
            if (id.includes('firebase/firestore') || id.includes('@firebase/firestore')) {
              return 'firebase-firestore';
            }
            if (id.includes('firebase/analytics') || id.includes('@firebase/analytics')) {
              return 'firebase-analytics';
            }
            if (id.includes('firebase') || id.includes('@firebase')) {
              return 'firebase-utils';
            }

            // Lucide React icons - separate chunk since it's large
            if (id.includes('lucide-react')) {
              return 'lucide-icons';
            }

            // Other vendor libraries
            return 'vendor-utils';
          }

          // Split pages into separate chunks
          if (id.includes('src/pages/')) {
            const pageName = id.split('/pages/')[1].split('.')[0].toLowerCase();
            return `page-${pageName}`;
          }

          // Split components by feature
          if (id.includes('src/components/')) {
            // Admin components
            if (id.includes('PricingManagement') || id.includes('DiscountCodeManagement') || id.includes('RealTimeAnalytics')) {
              return 'admin-components';
            }
            // Form components
            if (id.includes('Form') || id.includes('Input') || id.includes('Select') || id.includes('Textarea')) {
              return 'form-components';
            }
            // Performance monitoring (dev only)
            if (id.includes('PerformanceMonitor')) {
              return 'dev-components';
            }
          }

          // Split utilities
          if (id.includes('src/utils/')) {
            if (id.includes('performance')) {
              return 'performance-utils';
            }
            if (id.includes('serviceWorker')) {
              return 'sw-utils';
            }
          }
        }
      }
    },
    // Increase chunk size warning limit for vendor bundles
    // React vendor bundle is naturally large (~700KB) but gzips well to ~185KB
    chunkSizeWarningLimit: 800,
    // Enable source maps for production debugging (optional)
    sourcemap: false,
    // Optimize CSS
    cssMinify: true,
    // Optimize for modern browsers
    cssTarget: 'es2020'
  },
  // Optimize deps for faster dev server
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'lucide-react',
      'firebase/app',
      'firebase/auth',
      'firebase/firestore',
      'react-router-dom'
    ],
    exclude: ['firebase']
  },
  // Configure server for development
  server: {
    headers: {
      // Cache static assets for 1 year
      'Cache-Control': 'public, max-age=31536000, immutable'
    }
  },
  // Configure preview server
  preview: {
    headers: {
      'Cache-Control': 'public, max-age=31536000, immutable'
    }
  }
})
